{"name": "kkpm-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "codegen": "graphql-codegen --watch"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@hookform/resolvers": "^5.0.1", "@react-native-community/datetimepicker": "8.3.0", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@tanstack/react-query": "^4", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo": "^53.0.9", "expo-blur": "~14.1.4", "expo-camera": "~16.1.6", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "graphql": "^16.10.0", "graphql-request": "^5", "jotai": "^2.12.3", "jwt-decode": "^4.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.0", "react-native": "0.79.2", "react-native-calendars": "^1.1311.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zod": "^3.24.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/client-preset": "^4.2.6", "@graphql-codegen/typescript-react-query": "^6.1.0", "@parcel/watcher": "^2.4.1", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "graphql-codegen-typescript-client": "0.18.2", "graphql-codegen-typescript-common": "0.18.2", "jest": "^29.2.1", "jest-expo": "~53.0.5", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}