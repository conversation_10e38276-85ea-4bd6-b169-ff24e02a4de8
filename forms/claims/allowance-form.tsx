import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import React, { useState } from "react";
import * as z from "zod";
import {
  SubmitHandler,
  useFieldArray,
  useForm,
  UseFormReturn,
} from "react-hook-form";
import { useRouter } from "expo-router";
import { zodResolver } from "@hookform/resolvers/zod";
import { Colors } from "@/constants/Colors";
import {
  FormDatePicker,
  FormImagePicker,
  FormInput,
  FormSubmitButton,
} from "@/components/forms";
import { Ionicons } from "@expo/vector-icons";

const singleAllowanceSchema = z.object({
  date: z.string().min(1, { message: "Date is required" }),
  day: z.string().min(1, { message: "Day is required" }),
  jobNo: z.string().min(1, { message: "Job number is required" }),
  reason: z.string().min(1, { message: "Reason is required" }),
  workingHours: z.string().min(1, { message: "Working hours is required" }),
  amount: z.string().min(1, { message: "Amount is required" }),
  total: z.string().min(1, { message: "Total is required" }),
  invoice: z.array(z.string()).nullable(),
});

export const schema = z.object({
  allowances: z.array(singleAllowanceSchema),
});

type AllowanceForm = z.infer<typeof schema>;

const defaultAllowanceEntry: z.infer<typeof singleAllowanceSchema> = {
  date: "",
  day: "",
  jobNo: "",
  reason: "",
  workingHours: "",
  amount: "",
  total: "",
  invoice: null,
};

const defaultAllowancesValues: AllowanceForm = {
  allowances: [defaultAllowanceEntry],
};

export default function AllowanceForm() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<AllowanceForm>({
    defaultValues: defaultAllowancesValues,
    resolver: zodResolver(schema),
    mode: "onChange",
  });
  // Use fieldArray to handle multiple allowance entries
  const { fields, append, remove } = useFieldArray({
    control,
    name: "allowances",
  });

  // Handle form submission
  const onSubmit = (data: AllowanceForm) => {
    try {
      console.log(data.allowances);
    } catch (error) {
      console.log(error);
    }
  };

  // Add a new allowance entry
  const handleAddMore = () => {
    append(defaultAllowanceEntry);
  };

  return (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={styles.scrollContent}
    >
      {fields.map((field, index) => (
        <View key={field.id} style={styles.allowanceContainer}>
          {index > 0 && (
            <>
              <View style={styles.separator} />
              <View style={styles.allowanceHeader}>
                <Text style={styles.allowanceTitle}>Allowance {index + 1}</Text>
                <TouchableOpacity
                  onPress={() => remove(index)}
                  style={styles.removeButton}
                >
                  <Ionicons
                    name="close-circle"
                    size={24}
                    color={Colors.error || "red"}
                  />
                </TouchableOpacity>
              </View>
            </>
          )}

          {/* Date Input */}
          <FormDatePicker
            name={`allowances.${index}.date`}
            control={control}
            label="Date"
            placeholder="dd-mm-yy"
          />

          {/* Day Input */}
          <FormInput
            name={`allowances.${index}.day`}
            control={control}
            label="Day"
            placeholder="Enter"
          />

          {/* Job No Input */}
          <FormInput
            name={`allowances.${index}.jobNo`}
            control={control}
            label="Job No"
            placeholder="Enter job no"
          />

          {/* Reason Input */}
          <FormInput
            name={`allowances.${index}.reason`}
            control={control}
            label="Reason"
            placeholder="Type something..."
            multiline
            numberOfLines={3}
          />

          {/* Working Hours Input */}
          <FormInput
            name={`allowances.${index}.workingHours`}
            control={control}
            label="Working Hours"
            placeholder="Enter Hours"
            keyboardType="numeric"
          />

          {/* Amount Input */}
          <FormInput
            name={`allowances.${index}.amount`}
            control={control}
            label="Amount (RM)"
            placeholder="Enter Amount"
            keyboardType="numeric"
          />

          {/* Total Input */}
          <FormInput
            name={`allowances.${index}.total`}
            control={control}
            label="Total"
            placeholder="Enter amount"
            keyboardType="numeric"
          />

          {/* Upload Invoice */}
          <FormImagePicker
            name={`allowances.${index}.invoice`}
            control={control}
            label="Upload Invoice"
          />
        </View>
      ))}

      {/* Add More Button */}
      <TouchableOpacity
        style={styles.addMoreButton}
        onPress={handleAddMore}
        activeOpacity={0.7}
      >
        <Ionicons name="add-circle" size={20} color={Colors.primary} />
        <Text style={styles.addMoreText}>Add More</Text>
      </TouchableOpacity>

      {/* Submit Button */}
      <FormSubmitButton
        submitLabel="Submit"
        onSubmit={handleSubmit(onSubmit)}
        isSubmitting={isSubmitting}
        isValid={isValid}
        style={styles.submitButton}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 120, // Increased bottom padding
    paddingTop: 20,
  },

  allowanceContainer: {
    marginBottom: 20,
    paddingVertical: 16,
  },
  allowanceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  allowanceTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
  },
  removeButton: {
    padding: 4,
  },
  addMoreButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    marginBottom: 20,
  },
  addMoreText: {
    marginLeft: 8,
    fontSize: 16,
    color: Colors.primary,
    fontWeight: "500",
  },
  submitButton: {
    marginTop: 20,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.border,
    marginVertical: 16,
  },
  filterContainer: {
    paddingVertical: 8,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.textSecondary,
    marginLeft: 16,
    marginTop: 8,
    marginBottom: 4,
  },
  filterList: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
});
