import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
} from "react-native";
import { Link } from "expo-router";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  Easing,
} from "react-native-reanimated";
import { AnnouncementItem, Announcement } from "./AnnouncementItem";
import { Colors } from "@/constants/Colors";

interface AnnouncementSectionProps {
  announcements: Announcement[];
  onAnnouncementPress: (announcement: Announcement) => void;
}

/**
 * Announcement section with header and list of announcements with animations
 */
export function AnnouncementSection({
  announcements,
  onAnnouncementPress,
}: AnnouncementSectionProps) {
  // Animation for the header
  const headerOpacity = useSharedValue(0);
  const headerTranslateY = useSharedValue(-20);

  // Start animations when component mounts
  React.useEffect(() => {
    // Animate header
    headerOpacity.value = withTiming(1, {
      duration: 400,
      easing: Easing.out(Easing.ease),
    });
    headerTranslateY.value = withTiming(0, {
      duration: 400,
      easing: Easing.out(Easing.ease),
    });
  }, []);

  // Header animated style
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: headerTranslateY.value }],
  }));

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <Text style={styles.title}>Announcement</Text>
        <Link href="/announcements" asChild>
          <TouchableOpacity>
            <Text style={styles.viewAll}>View All</Text>
          </TouchableOpacity>
        </Link>
      </Animated.View>

      <FlatList
        data={announcements}
        keyExtractor={(item) => item.id}
        renderItem={({ item, index }) => (
          <AnnouncementItem announcement={item} onPress={onAnnouncementPress} />
        )}
        scrollEnabled={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
    backgroundColor: Colors.white,
    borderRadius: 12,
    overflow: "hidden",
    marginHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.text,
  },
  viewAll: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: "500",
  },
});
