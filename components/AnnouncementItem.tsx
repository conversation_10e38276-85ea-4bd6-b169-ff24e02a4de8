import React from "react";
import { StyleSheet, View, Text, Image, TouchableOpacity } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withSpring,
  Easing,
} from "react-native-reanimated";
import { Colors } from "@/constants/Colors";

export interface Announcement {
  id: string;
  sender: {
    name: string;
    avatar: string;
  };
  message: string;
  time: string;
  isUnread?: boolean;
}

interface AnnouncementItemProps {
  announcement: Announcement;
  onPress: (announcement: Announcement) => void;
}

/**
 * Announcement item component for the announcement list with animations
 */
export function AnnouncementItem({
  announcement,
  onPress,
}: AnnouncementItemProps) {
  const { sender, message, time, isUnread } = announcement;

  // Animation values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);

  // Handle press animation
  const handlePress = () => {
    // Scale animation
    scale.value = withSequence(
      withTiming(0.98, { duration: 100, easing: Easing.inOut(Easing.quad) }),
      withTiming(1, { duration: 200, easing: Easing.out(Easing.quad) })
    );

    // Subtle horizontal movement
    translateX.value = withSequence(
      withTiming(5, { duration: 100 }),
      withTiming(0, { duration: 200, easing: Easing.out(Easing.quad) })
    );

    // Call the onPress callback
    onPress(announcement);
  };

  // Animated style
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }, { translateX: translateX.value }],
  }));

  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        style={styles.container}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <Image source={{ uri: sender.avatar }} style={styles.avatar} />

        <View style={styles.contentContainer}>
          <View style={styles.header}>
            <Text style={styles.senderName}>{sender.name}</Text>
            <Text style={styles.time}>{time}</Text>
          </View>

          <Text style={styles.message} numberOfLines={1}>
            {message}
          </Text>
        </View>

        {isUnread && <View style={styles.unreadIndicator} />}
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  senderName: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.text,
  },
  time: {
    fontSize: 12,
    color: Colors.textLight,
  },
  message: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.notification,
    marginLeft: 8,
  },
});
