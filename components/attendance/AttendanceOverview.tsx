import React from "react";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  FadeIn,
} from "react-native-reanimated";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { Card } from "@/components/ui/Card";

// Define attendance status types
export enum AttendanceStatus {
  ONTIME = "ontime",
  LATE = "late",
  ABSENT = "absent",
  FUTURE = "future",
}

// Define attendance data structure
export interface DailyAttendance {
  day: string;
  records: AttendanceStatus[];
}

// Define attendance summary data structure
export interface AttendanceSummary {
  ontime: number;
  late: number;
  absent: number;
}

interface AttendanceOverviewProps {
  data: {
    dailyAttendance: DailyAttendance[];
    summary: AttendanceSummary;
  };
  // Clock-related props
  isClockedIn: boolean;
  clockInTime: Date | null;
  clockOutTime: Date | null;
  workHours: number | null;
  // Callback functions
  onClockIn: () => void;
  onClockOut: () => void;
}

/**
 * Attendance Overview Component
 * Displays a grid of attendance records and summary statistics
 */
export function AttendanceOverview({
  data,
  isClockedIn,
  clockInTime,
  clockOutTime,
  workHours,
  onClockIn,
  onClockOut,
}: AttendanceOverviewProps) {
  // Animation values
  const cardScale = useSharedValue(0.95);
  const cardOpacity = useSharedValue(0);
  const buttonScale = useSharedValue(0.95);

  // Start animations when component mounts
  React.useEffect(() => {
    // Animate card
    cardScale.value = withTiming(1, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
    cardOpacity.value = withTiming(1, { duration: 600 });
    buttonScale.value = withTiming(1, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, []);

  // Card animated style
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ scale: cardScale.value }],
  }));

  // Button animated style
  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ scale: buttonScale.value }],
  }));

  // Format time for display
  const formatTime = (date: Date | null): string => {
    if (!date) return "";
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  // Get color for attendance status
  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.ONTIME:
        return Colors.primary;
      case AttendanceStatus.LATE:
        // Lighter orange color
        return "#FFB74D";
      case AttendanceStatus.ABSENT:
        return Colors.error;
      case AttendanceStatus.FUTURE:
        return Colors.lightGray;
      default:
        return Colors.lightGray;
    }
  };

  // Calculate percentage for summary
  const calculatePercentage = (value: number) => {
    const total = data.summary.ontime + data.summary.late + data.summary.absent;
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  return (
    <View>
      {/* Clock In/Out Buttons */}
      <Animated.View
        style={[buttonAnimatedStyle, styles.clockButtonsContainer]}
      >
        <TouchableOpacity
          style={[
            styles.clockButton,
            styles.clockInButton,
            isClockedIn && styles.clockButtonDisabled,
          ]}
          onPress={onClockIn}
          disabled={isClockedIn}
        >
          <Ionicons
            name="time-outline"
            size={20}
            color={isClockedIn ? Colors.textSecondary : Colors.primary}
          />
          <Text
            style={[
              styles.clockButtonText,
              isClockedIn
                ? styles.clockButtonTextDisabled
                : styles.clockInButtonText,
            ]}
          >
            {clockInTime ? `In: ${formatTime(clockInTime)}` : "Clock In"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.clockButton,
            styles.clockOutButton,
            !isClockedIn && styles.clockButtonDisabled,
          ]}
          onPress={onClockOut}
          disabled={!isClockedIn}
        >
          <Ionicons
            name="exit-outline"
            size={20}
            color={!isClockedIn ? Colors.textSecondary : Colors.error}
          />
          <Text
            style={[
              styles.clockButtonText,
              !isClockedIn
                ? styles.clockButtonTextDisabled
                : styles.clockOutButtonText,
            ]}
          >
            Clock Out
          </Text>
        </TouchableOpacity>
      </Animated.View>

      {/* Attendance Card */}
      <Animated.View style={cardAnimatedStyle}>
        <Card style={styles.card}>
          <Text style={styles.title}>Attendance</Text>

          {/* Attendance Grid */}
          <View style={styles.gridContainer}>
            {data.dailyAttendance.map((day, dayIndex) => (
              <View key={dayIndex} style={styles.dayRow}>
                <View style={styles.dayRecords}>
                  {day.records.map((status, recordIndex) => (
                    <Animated.View
                      key={`${dayIndex}-${recordIndex}`}
                      entering={FadeIn.delay(
                        100 * (dayIndex + recordIndex)
                      ).duration(300)}
                      style={[
                        styles.statusBox,
                        { backgroundColor: getStatusColor(status) },
                      ]}
                    />
                  ))}
                </View>
              </View>
            ))}
          </View>

          {/* Summary Section */}
          <View style={styles.summaryContainer}>
            <View style={styles.summaryItem}>
              <View style={styles.summaryLabelContainer}>
                <View
                  style={[
                    styles.summaryIndicator,
                    { backgroundColor: Colors.primary },
                  ]}
                />
                <Text style={styles.summaryLabel}>On-time</Text>
              </View>
              <Text style={styles.summaryValue}>
                {calculatePercentage(data.summary.ontime)}%
              </Text>
            </View>

            <View style={styles.summaryItem}>
              <View style={styles.summaryLabelContainer}>
                <View
                  style={[
                    styles.summaryIndicator,
                    { backgroundColor: "#FFB74D" },
                  ]}
                />
                <Text style={styles.summaryLabel}>Late</Text>
              </View>
              <Text style={styles.summaryValue}>
                {calculatePercentage(data.summary.late)}%
              </Text>
            </View>

            <View style={styles.summaryItem}>
              <View style={styles.summaryLabelContainer}>
                <View
                  style={[
                    styles.summaryIndicator,
                    { backgroundColor: Colors.error },
                  ]}
                />
                <Text style={styles.summaryLabel}>Absent</Text>
              </View>
              <Text style={styles.summaryValue}>
                {calculatePercentage(data.summary.absent)}%
              </Text>
            </View>
          </View>
        </Card>
      </Animated.View>

      {/* Work Hours Summary Card - Only show when there are work hours to display */}
      {workHours !== null && (
        <Animated.View style={[cardAnimatedStyle, { marginTop: 16 }]}>
          <Card style={styles.card}>
            <Text style={styles.title}>Today's Work Summary</Text>

            <View style={styles.workSummaryContainer}>
              <View style={styles.workSummaryRow}>
                <Text style={styles.workSummaryLabel}>Clock In:</Text>
                <Text style={styles.workSummaryValue}>
                  {formatTime(clockInTime)}
                </Text>
              </View>

              <View style={styles.workSummaryRow}>
                <Text style={styles.workSummaryLabel}>Clock Out:</Text>
                <Text style={styles.workSummaryValue}>
                  {formatTime(clockOutTime)}
                </Text>
              </View>

              <View style={[styles.workSummaryRow, styles.totalHoursRow]}>
                <Text style={styles.totalHoursLabel}>Total Hours:</Text>
                <Text style={styles.totalHoursValue}>{workHours} hrs</Text>
              </View>
            </View>
          </Card>
        </Animated.View>
      )}
    </View>
  );
}

// Mock data for the component
export const mockAttendanceData = {
  dailyAttendance: [
    {
      day: "",
      records: [
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ONTIME,
        AttendanceStatus.LATE,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ONTIME,
      ],
    },
    {
      day: "",
      records: [
        AttendanceStatus.ONTIME,
        AttendanceStatus.LATE,
        AttendanceStatus.ONTIME,
        AttendanceStatus.LATE,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ABSENT,
      ],
    },
    {
      day: "",
      records: [
        AttendanceStatus.LATE,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ONTIME,
        AttendanceStatus.FUTURE,
        AttendanceStatus.FUTURE,
        AttendanceStatus.FUTURE,
        AttendanceStatus.FUTURE,
        AttendanceStatus.FUTURE,
      ],
    },
  ],
  summary: {
    ontime: 15,
    late: 0,
    absent: 0,
  },
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 16,
    marginVertical: 12,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 16,
  },
  gridContainer: {
    marginBottom: 20,
  },
  dayRow: {
    marginBottom: 12,
  },
  dayRecords: {
    flex: 1,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 4,
  },
  statusBox: {
    width: 24,
    height: 24,
    borderRadius: 4,
  },
  summaryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  summaryItem: {
    flex: 1,
    alignItems: "center",
  },
  summaryLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  summaryIndicator: {
    width: 16,
    height: 4,
    borderRadius: 2,
    marginRight: 6,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
  },
  // Clock in/out button styles
  clockButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: 16,
    marginVertical: 12,
    gap: 12,
  },
  clockButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
    backgroundColor: Colors.white,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  clockInButton: {
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  clockOutButton: {
    borderWidth: 1,
    borderColor: Colors.error,
  },
  clockButtonDisabled: {
    backgroundColor: Colors.white,
    borderColor: Colors.lightGray,
    borderWidth: 1,
  },
  clockButtonText: {
    fontWeight: "600",
    fontSize: 14,
  },
  clockButtonTextDisabled: {
    color: Colors.textSecondary,
  },
  clockInButtonText: {
    color: Colors.primary,
  },
  clockOutButtonText: {
    color: Colors.error,
  },
  // Work summary styles
  workSummaryContainer: {
    marginTop: 16,
  },
  workSummaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  workSummaryLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  workSummaryValue: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: "500",
  },
  totalHoursRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    borderBottomWidth: 0,
  },
  totalHoursLabel: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.text,
  },
  totalHoursValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.primary,
  },
});
