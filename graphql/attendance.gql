query Attendance($attendanceInput: AttendanceInput!) {
  attendance(attendanceInput: $attendanceInput) {
    id
    createdAt
    updatedAt
    date
    endTime
    location {
      id
      name
    }
    overTime
    overTimeSpentInMinutes
    startTime
    timeSpentInMinutes
  }
}

mutation ClockIn($clockInInput: ClockInInput!) {
  clockIn(clockInInput: $clockInInput) {
    id
  }
}

mutation ClockOut($clockOut: ClockOutInput!) {
  clockOut(clockOutInput: $clockOut) {
    id
  }
}
