mutation CreateClaim($input: ClaimInput!) {
  createClaim(input: $input) {
    _id
    id
    createdAt
    updatedAt
    status
    claimType
    claimData {
      ... on AllowanceClaim {
        amount
        purpose
        from
        to
        workingHours
      }
      ... on TravelClaim {
        amount
        purpose
        from
        to
        client
        toll
        distance
      }
      ... on ExpenseClaim {
        amount
        purpose
        items
        date
      }
      ... on SiteClaim {
        amount
        purpose
        items
        site {
          _id
          id
          createdAt
          updatedAt
          name
          description
          address
          emergencyContact
        }
      }
    }
    rejectedReason
  }
}
