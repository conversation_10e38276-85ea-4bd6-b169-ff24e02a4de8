import React, { useEffect } from "react";
import { StyleSheet, ScrollView, View, SafeAreaView } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withD<PERSON>y,
  Easing,
} from "react-native-reanimated";
import { StatusBar } from "expo-status-bar";
import { Redirect } from "expo-router";
import { ProfileHeader } from "@/components/ProfileHeader";
import { MenuGrid } from "@/components/MenuGrid";
import { AnnouncementSection } from "@/components/AnnouncementSection";
import { Announcement } from "@/components/AnnouncementItem";
import { Colors } from "@/constants/Colors";
import { useSession } from "@/providers/auth-provider";
import { useMeQuery } from "@/generated/graphql";

// Sample data for menu items
const menuItems = [
  { icon: "people-outline", label: "Attendance", href: "/attendance" },
  { icon: "calendar-outline", label: "Leave", href: "/leave" },
  { icon: "time-outline", label: "Shifts", href: "/shifts" },
  { icon: "card-outline", label: "Payments", href: "/payments" },
  { icon: "document-text-outline", label: "Documents", href: "/documents" },
  { icon: "calendar-clear-outline", label: "Calendar", href: "/calendar" },
  { icon: "mail-outline", label: "Inbox", href: "/inbox" },
  { icon: "person-outline", label: "Account", href: "/account" },
];

// Sample data for announcements
const announcements: Announcement[] = [
  {
    id: "1",
    sender: {
      name: "Rucas Royal",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
    },
    message: "Is this jacket waterproof and warm enough for winter?",
    time: "01:09 am",
    isUnread: true,
  },
  {
    id: "2",
    sender: {
      name: "Leslie Alexander",
      avatar: "https://randomuser.me/api/portraits/women/44.jpg",
    },
    message: "Do you have any new arrivals in medium size?",
    time: "01:08 pm",
    isUnread: true,
  },
  {
    id: "3",
    sender: {
      name: "Floyd Miles",
      avatar: "https://randomuser.me/api/portraits/men/53.jpg",
    },
    message: "I need a pair of comfortable jeans for everyday wear.",
    time: "06:32 pm",
    isUnread: true,
  },
  {
    id: "4",
    sender: {
      name: "Guy Hawkins",
      avatar: "https://randomuser.me/api/portraits/men/91.jpg",
    },
    message: "I'm attending a wedding soon. Do you have formal wear?",
    time: "08:20 pm",
  },
  {
    id: "5",
    sender: {
      name: "Wade Warren",
      avatar: "https://randomuser.me/api/portraits/men/41.jpg",
    },
    message: "What are your best-selling accessories this season?",
    time: "10:32 pm",
  },
];

/**
 * Home screen component with user profile, menu grid, and announcements
 */
export default function HomeScreen() {
  // Get session from auth provider
  const { session, isLoading } = useSession();
  const { data: userData } = useMeQuery();

  // Redirect to login if not authenticated
  if (!isLoading && (!session || session === null)) {
    return <Redirect href="/login" />;
  }

  // Animation values - only use translation animations, no opacity/fade
  const profileTranslateY = useSharedValue(20);
  const announcementsTranslateY = useSharedValue(30);

  // Animation styles - no opacity animations
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    // No animations for header background
  }));

  const profileAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: profileTranslateY.value }],
  }));

  const menuAnimatedStyle = useAnimatedStyle(() => ({
    // No animations for menu container - individual items animate themselves
  }));

  const announcementsAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: announcementsTranslateY.value }],
  }));

  // Start animations when component mounts
  useEffect(() => {
    // Only slide animations, no fading
    profileTranslateY.value = withDelay(
      200,
      withTiming(0, { duration: 800, easing: Easing.out(Easing.ease) })
    );

    announcementsTranslateY.value = withDelay(
      800,
      withTiming(0, { duration: 800, easing: Easing.out(Easing.ease) })
    );
  }, []);

  // Handle announcement press
  const handleAnnouncementPress = (announcement: Announcement) => {
    // Navigate to announcement details or show modal
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />

      <Animated.View style={[styles.headerBackground, headerAnimatedStyle]} />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Profile Header - Animated */}
        <Animated.View style={profileAnimatedStyle}>
          <ProfileHeader
            name={session!.fullname}
            role="default"
            avatarUrl={userData?.me.profilePicture!}
          />
        </Animated.View>

        {/* Menu Grid - Animated */}
        <Animated.View style={menuAnimatedStyle}>
          <MenuGrid items={menuItems} />
        </Animated.View>

        {/* Announcements - Animated */}
        <Animated.View style={announcementsAnimatedStyle}>
          <AnnouncementSection
            announcements={announcements}
            onAnnouncementPress={handleAnnouncementPress}
          />
        </Animated.View>

        {/* Add padding at the bottom for scrolling */}
        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerBackground: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: 200,
    backgroundColor: Colors.primary,
    // On Android, zIndex: -1 can cause issues with rendering
    // Using a higher zIndex and ensuring it's behind other elements
    zIndex: 0,
  },
  scrollView: {
    flex: 1,
    zIndex: 1, // Ensure the scroll view is above the header background
  },
  scrollContent: {
    paddingBottom: 20,
  },
  bottomPadding: {
    height: 40,
  },
});
