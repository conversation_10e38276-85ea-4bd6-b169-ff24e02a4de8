import {
  AttendanceOverview,
  mockAttendanceData,
} from "@/components/attendance/AttendanceOverview";
import LoadingScreen from "@/components/LoadingView";
import { Colors } from "@/constants/Colors";
import {
  useAttendanceQuery,
  useClockInMutation,
  // useClockOutMutation,
  useShiftsQuery,
} from "@/generated/graphql";
import useDateNow from "@/hooks/useDate";
import { subHours } from "date-fns";
import { StatusBar } from "expo-status-bar";
import React, { useMemo } from "react";
import { SafeAreaView, ScrollView, StyleSheet, Text, View } from "react-native";

export default function AttendanceScreen() {
  const { mutateAsync: clockInAsync } = useClockInMutation();
  // const { mutateAsync: clockOutAsync } = useClockOutMutation();

  const dateNow = useDateNow();

  // GraphQL queries for shifts and attendance data
  const {
    data: shifts,
    isLoading: shiftsLoading,
    error, // TODO: Add error handling
  } = useShiftsQuery(
    {
      input: {
        startDateTime: subHours(dateNow, 24),
        endDateTime: dateNow,
      },
    },
    { initialData: { shifts: [] } }
  );

  const {
    data: attendanceData,
    isLoading: attendanceLoading,
    error: attendanceError, // TODO: Add error handling
  } = useAttendanceQuery(
    {
      attendanceInput: { shiftId: shifts!.shifts?.[0]?.id },
    },
    { initialData: { attendance: [] }, enabled: !!shifts!.shifts?.[0]?.id }
  );

  // Check if user is currently clocked in based on backend data
  const { clockInTime, clockOutTime, isClockedIn } = useMemo(() => {
    if (!attendanceData)
      return { isClockedIn: false, clockInTime: null, clockOutTime: null };
    const isClockedIn = attendanceData.attendance.some(
      (attendance) => !attendance.endTime
    );
    const clockInTime =
      attendanceData.attendance.find((attendance) => !attendance.endTime)
        ?.startTime || null;
    const clockOutTime =
      attendanceData.attendance.find((attendance) => !attendance.endTime)
        ?.endTime || null;

    return { isClockedIn, clockInTime, clockOutTime };
  }, [attendanceData]);

  // If user is clocked in show work hours
  const workHours = useMemo(() => {
    if (!attendanceData) return null;
    const activeAttendance = attendanceData.attendance.find(
      (attendance) => !attendance.endTime
    );
    if (!activeAttendance) return null;
    const diffMs = dateNow.getTime() - activeAttendance.startTime.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    return parseFloat(diffHours.toFixed(2));
  }, [attendanceData, dateNow]);

  // Handle clock in
  const handleClockIn = async () => {
    try {
      const date = new Date();
      const locationId = shifts?.shifts?.[0]?.location?.id;
      const shiftId = shifts?.shifts?.[0]?.id;

      if (!shiftId || !locationId) throw alert("Shift/Location ID is missing");

      await clockInAsync({
        clockInInput: { date, locationId, shiftId, base64Img: "" },
      });
    } catch (error) {
      console.error("Clock in failed", error);
    }
  };

  // Handle clock out
  const handleClockOut = async () => {
    try {
      // TODO: Implement clock out mutation with proper parameters
      // await clockOutAsync({
      //   clockOut: { /* required parameters */ }
      // });
      console.log("Clock out clicked - implement mutation");
    } catch (error) {
      console.error("Clock out failed", error);
    }
  };

  // Show loading screen while fetching data
  if (shiftsLoading || attendanceLoading) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <View style={styles.header}>
        <Text style={styles.title}>Attendance</Text>
        <Text style={styles.subtitle}>Track your attendance records</Text>
      </View>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Attendance Overview Component */}
        <AttendanceOverview
          data={mockAttendanceData}
          isClockedIn={isClockedIn}
          clockInTime={clockInTime}
          clockOutTime={clockOutTime}
          workHours={workHours}
          onClockIn={handleClockIn}
          onClockOut={handleClockOut}
        />

        {/* Additional attendance content can be added here */}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F0F0", // Very light grey background
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 10,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 16,
  },
});
