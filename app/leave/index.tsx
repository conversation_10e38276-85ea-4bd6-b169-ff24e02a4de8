import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from "react-native";
import { StatusBar } from "@/components/ui/StatusBar";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withDelay,
  withSpring,
} from "react-native-reanimated";
import { Colors } from "@/constants/Colors";
import {
  useGetLeavesQuery,
  LeaveType as GraphQLLeaveType,
  LeaveStatus as GraphQLLeaveStatus,
  useMeQuery,
} from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";
// Define leave types directly in this file for now
enum LeaveType {
  FULLDAY = "fullday",
  HALFDAY = "halfday",
}

enum LeaveStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

// Filter options for leave history
const statusFilterOptions = [
  { id: "all", label: "All" },
  { id: "pending", label: "Pending" },
  { id: "approved", label: "Approved" },
  { id: "rejected", label: "Rejected" },
];

// Filter options for leave type
const typeFilterOptions = [
  { id: "all", label: "All Types" },
  { id: "fullday", label: "Full Day" },
  { id: "halfday", label: "Half Day" },
];

/**
 * Leave Item Component with animations
 */
interface LeaveItemProps {
  leave: {
    id: string;
    reason: string;
    leaveType: LeaveType;
    startDateTime: Date;
    endDateTime: Date;
    leaveStatus: LeaveStatus;
    rejectedReason?: string;
  };
  index: number;
  onPress: (leave: any) => void;
}

function LeaveItem({ leave, onPress }: LeaveItemProps) {
  // Handle press
  const handlePress = () => {
    // Call the onPress callback
    onPress(leave);
  };

  // Format date range
  const formatDateRange = (start: Date, end: Date) => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    const options: Intl.DateTimeFormatOptions = {
      month: "short",
      day: "numeric",
    };
    const startFormatted = startDate.toLocaleDateString("en-US", options);

    if (startDate.getTime() === endDate.getTime()) {
      return startFormatted;
    }
    const endFormatted = endDate.toLocaleDateString("en-US", options);
    return `${startFormatted} - ${endFormatted}`;
  };

  // Get status color
  const getStatusColor = (status: LeaveStatus) => {
    switch (status) {
      case LeaveStatus.APPROVED:
        return Colors.success;
      case LeaveStatus.REJECTED:
        return Colors.error;
      case LeaveStatus.PENDING:
      default:
        return Colors.warning;
    }
  };

  // Get leave type label
  const getLeaveTypeLabel = (type: LeaveType) => {
    return type === LeaveType.FULLDAY ? "Full Day" : "Half Day";
  };

  return (
    <View style={styles.leaveItemContainer}>
      <TouchableOpacity
        style={styles.leaveItem}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={styles.leaveHeader}>
          <Text style={styles.leaveReason}>{leave.reason}</Text>
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(leave.leaveStatus) },
            ]}
          >
            <Text style={styles.statusText}>
              {leave.leaveStatus.charAt(0).toUpperCase() +
                leave.leaveStatus.slice(1)}
            </Text>
          </View>
        </View>

        <View style={styles.leaveDetails}>
          <View style={styles.detailRow}>
            <Ionicons
              name="calendar-outline"
              size={16}
              color={Colors.textSecondary}
            />
            <Text style={styles.detailText}>
              {formatDateRange(leave.startDateTime, leave.endDateTime)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Ionicons
              name="time-outline"
              size={16}
              color={Colors.textSecondary}
            />
            <Text style={styles.detailText}>
              {getLeaveTypeLabel(leave.leaveType)}
            </Text>
          </View>
        </View>

        {leave.rejectedReason && (
          <View style={styles.rejectionContainer}>
            <Text style={styles.rejectionReason}>
              Reason: {leave.rejectedReason}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
}

/**
 * Filter Button Component
 */
export interface FilterButtonProps {
  option: { id: string; label: string };
  isActive: boolean;
  onPress: (id: string) => void;
}

export function FilterButton({ option, isActive, onPress }: FilterButtonProps) {
  return (
    <TouchableOpacity
      style={[styles.filterButton, isActive && styles.activeFilterButton]}
      onPress={() => onPress(option.id)}
    >
      <Text
        style={[
          styles.filterButtonText,
          isActive && styles.activeFilterButtonText,
        ]}
      >
        {option.label}
      </Text>
    </TouchableOpacity>
  );
}

/**
 * Leave History Screen
 */
export default function LeaveHistoryScreen() {
  const router = useRouter();
  const { session } = useSession();
  const [activeStatusFilter, setActiveStatusFilter] = useState("all");
  const [activeTypeFilter, setActiveTypeFilter] = useState("all");

  // Animation values for the FAB
  const fabScale = useSharedValue(0);
  const fabTranslateY = useSharedValue(100);
  const { data: me } = useMeQuery();

  // Fetch leaves using GraphQL query
  const { data, isLoading, error } = useGetLeavesQuery(
    {
      input: {
        // Use the user ID from the me query
        user: me?.me.id,
        leaveStatus:
          activeStatusFilter !== "all"
            ? activeStatusFilter.toUpperCase()
            : undefined,
        leaveType:
          activeTypeFilter !== "all"
            ? activeTypeFilter === "fullday"
              ? GraphQLLeaveType.Fullday
              : GraphQLLeaveType.Halfday
            : undefined,
      },
    },
    {
      // Don't refetch on window focus
      refetchOnWindowFocus: false,
      // Enable the query only when we have a session and the user ID
      enabled: !!session && !!me?.me?.id,
    }
  );

  // Start animations when component mounts
  React.useEffect(() => {
    // Animate FAB with a spring effect
    fabScale.value = withDelay(
      500,
      withSpring(1, { damping: 8, stiffness: 100 })
    );

    fabTranslateY.value = withDelay(
      500,
      withSpring(0, { damping: 12, stiffness: 100 })
    );
  }, []);

  // FAB animated style
  const fabAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: fabScale.value }, { translateY: fabTranslateY.value }],
  }));

  // Map GraphQL leave data to the format expected by the LeaveItem component
  const mapLeaveData = () => {
    if (!data || !data.leaves) return [];

    return data.leaves.map((leave) => ({
      id: leave.id,
      reason: leave.reason,
      leaveType:
        leave.leaveType === GraphQLLeaveType.Fullday
          ? LeaveType.FULLDAY
          : LeaveType.HALFDAY,
      startDateTime: new Date(leave.startDateTime),
      endDateTime: new Date(leave.endDateTime),
      leaveStatus: mapLeaveStatus(leave.leaveStatus),
      rejectedReason: leave.rejectedReason || undefined,
    }));
  };

  // Map GraphQL leave status to app leave status
  const mapLeaveStatus = (
    status: GraphQLLeaveStatus | null | undefined
  ): LeaveStatus => {
    if (!status) return LeaveStatus.PENDING;

    switch (status) {
      case GraphQLLeaveStatus.Approved:
        return LeaveStatus.APPROVED;
      case GraphQLLeaveStatus.Rejected:
        return LeaveStatus.REJECTED;
      case GraphQLLeaveStatus.Pending:
      default:
        return LeaveStatus.PENDING;
    }
  };

  // Get filtered leaves
  const leaves = mapLeaveData();

  // We're already filtering by status and type in the API query
  // This is just a fallback in case we need to filter locally
  const filteredLeaves = leaves;

  // Handle leave item press
  const handleLeavePress = (leave: any) => {
    router.push({
      pathname: "/leave/[id]",
      params: { id: leave.id },
    });
  };

  // Handle request new leave button press
  const handleRequestLeave = () => {
    router.push("/leave/request");
  };

  // Handle back button press
  const handleBackPress = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Leave History</Text>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {/* Status Filters */}
        <Text style={styles.filterLabel}>Status:</Text>
        <FlatList
          data={statusFilterOptions}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterList}
          renderItem={({ item }) => (
            <FilterButton
              option={item}
              isActive={activeStatusFilter === item.id}
              onPress={setActiveStatusFilter}
            />
          )}
        />

        {/* Type Filters */}
        <Text style={styles.filterLabel}>Type:</Text>
        <FlatList
          data={typeFilterOptions}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterList}
          renderItem={({ item }) => (
            <FilterButton
              option={item}
              isActive={activeTypeFilter === item.id}
              onPress={setActiveTypeFilter}
            />
          )}
        />
      </View>

      {/* Loading State */}
      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading leave requests...</Text>
        </View>
      )}

      {/* Error State */}
      {error && (
        <View style={styles.errorContainer}>
          <Ionicons
            name="alert-circle-outline"
            size={64}
            color={Colors.error}
          />
          <Text style={styles.errorText}>Failed to load leave requests</Text>
          <Text style={styles.errorSubText}>
            {error instanceof Error ? error.message : "Unknown error occurred"}
          </Text>
        </View>
      )}

      {/* Leave List */}
      {!isLoading && !error && (
        <FlatList
          data={filteredLeaves}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.leaveList}
          renderItem={({ item, index }) => (
            <LeaveItem leave={item} index={index} onPress={handleLeavePress} />
          )}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons
                name="calendar-outline"
                size={64}
                color={Colors.textLight}
              />
              <Text style={styles.emptyText}>No leave requests found</Text>
            </View>
          }
        />
      )}

      {/* FAB for requesting new leave */}
      <Animated.View style={[styles.fab, fabAnimatedStyle]}>
        <TouchableOpacity
          style={styles.fabButton}
          onPress={handleRequestLeave}
          activeOpacity={0.8}
        >
          <Ionicons name="add" size={24} color={Colors.white} />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.white,
  },
  filterContainer: {
    paddingVertical: 12,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.textSecondary,
    marginLeft: 16,
    marginTop: 8,
    marginBottom: 4,
  },
  filterList: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: Colors.lightGray,
  },
  activeFilterButton: {
    backgroundColor: Colors.primaryLight,
  },
  filterButtonText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  activeFilterButtonText: {
    color: Colors.primaryDark,
    fontWeight: "500",
  },
  leaveList: {
    padding: 16,
  },
  leaveItemContainer: {
    marginBottom: 16,
  },
  leaveItem: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  leaveHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  leaveReason: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
    color: Colors.white,
  },
  leaveDetails: {
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  detailText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginLeft: 8,
  },
  rejectionContainer: {
    marginTop: 8,
    padding: 8,
    backgroundColor: "rgba(255,0,0,0.05)",
    borderRadius: 8,
  },
  rejectionReason: {
    fontSize: 14,
    color: Colors.error,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textLight,
    marginTop: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  errorText: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.error,
    marginTop: 16,
  },
  errorSubText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 8,
    textAlign: "center",
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
});
