import React, { useState } from "react";
import { StyleSheet, View, Text, TouchableOpacity, Alert } from "react-native";
import { StatusBar } from "@/components/ui/StatusBar";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Colors } from "@/constants/Colors";
import {
  Form,
  FormInput,
  FormRadioGroup,
  FormDatePicker,
  FormSubmitButton,
} from "@/components/forms";
import {
  LeaveType as AppLeaveType,
  leaveRequestSchema,
  LeaveRequestFormData,
  defaultLeaveRequestValues,
} from "./schemas";
import {
  useCreateLeaveMutation,
  LeaveType as GraphQLLeaveType,
  useMeQuery,
} from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";

/**
 * Request Leave Screen
 */
export default function RequestLeaveScreen() {
  const router = useRouter();
  const { session } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: me } = useMeQuery();

  // Initialize the create leave mutation
  const { mutateAsync: createLeave } = useCreateLeaveMutation({
    onSuccess: () => {
      // Show success message
      Alert.alert(
        "Success",
        "Your leave request has been submitted successfully",
        [
          {
            text: "OK",
            onPress: () => {
              // Navigate back
              router.back();
            },
          },
        ]
      );
    },
    onError: (error) => {
      // Show error message
      Alert.alert(
        "Error",
        error instanceof Error
          ? error.message
          : "Failed to submit leave request"
      );
    },
  });

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    watch,
    formState: { isValid },
  } = useForm<LeaveRequestFormData>({
    defaultValues: defaultLeaveRequestValues,
    resolver: zodResolver(leaveRequestSchema),
    mode: "onChange",
  });

  // Watch form values for conditional rendering
  const leaveType = watch("leaveType");

  // Handle form submission
  const onSubmit = async (data: LeaveRequestFormData) => {
    try {
      setIsSubmitting(true);

      // Convert form data to the format expected by the GraphQL mutation
      const startDateTime = new Date(data.startDate);
      // For half-day leave, end date is the same as start date
      const endDateTime = data.endDate
        ? new Date(data.endDate)
        : new Date(data.startDate);

      // Set end time to end of day for full day leave
      if (data.leaveType === AppLeaveType.FULLDAY && endDateTime) {
        endDateTime.setHours(23, 59, 59, 999);
      }

      // Map the app's LeaveType enum to the GraphQL LeaveType enum
      const graphqlLeaveType =
        data.leaveType === AppLeaveType.FULLDAY
          ? GraphQLLeaveType.Fullday
          : GraphQLLeaveType.Halfday;

      // Check if we have the user ID from the me query
      if (!me?.me?.id) {
        throw new Error("User ID not available. Please try again later.");
      }

      // Submit the leave request
      await createLeave({
        input: {
          // Use the actual user ID from the me query
          user: me.me.id,
          reason: data.reason,
          leaveType: graphqlLeaveType,
          startDateTime: startDateTime,
          endDateTime: endDateTime,
        },
      });

      // Success is handled in the onSuccess callback
    } catch (error) {
      // Error is handled in the onError callback
      console.error("Error submitting leave request:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle back button press
  const handleBackPress = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Request Leave</Text>
      </View>

      <Form>
        {/* Reason Input */}
        <FormInput
          name="reason"
          control={control}
          label="Reason for Leave"
          placeholder="Enter reason for leave"
          multiline
          numberOfLines={3}
        />

        {/* Leave Type Selection */}
        <FormRadioGroup
          name="leaveType"
          control={control}
          label="Leave Type"
          options={[
            { label: "Full Day", value: AppLeaveType.FULLDAY },
            { label: "Half Day", value: AppLeaveType.HALFDAY },
          ]}
        />

        {/* Date Selection */}
        <FormDatePicker
          name="startDate"
          control={control}
          label={leaveType === AppLeaveType.FULLDAY ? "Date Range" : "Date"}
          placeholder={
            leaveType === AppLeaveType.FULLDAY
              ? "Select date range"
              : "Select date"
          }
          isRange={leaveType === AppLeaveType.FULLDAY}
          endDateName="endDate"
          minDate={new Date().toISOString().split("T")[0]}
        />

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Submit Request"
          cancelLabel="Cancel"
          onSubmit={handleSubmit(onSubmit)}
          onCancel={handleBackPress}
          isSubmitting={isSubmitting}
          isValid={isValid && !!me?.me?.id}
          submitIcon="paper-plane-outline"
          cancelIcon="close-outline"
        />
      </Form>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "semibold",
    color: Colors.white,
  },
});
