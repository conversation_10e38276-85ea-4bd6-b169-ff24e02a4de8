import { z } from "zod";

// Define leave types
export enum LeaveType {
  FULLDAY = "fullday",
  HALFDAY = "halfday",
}

// Define leave status
export enum LeaveStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

// Schema for leave request form
export const leaveRequestSchema = z
  .object({
    reason: z
      .string()
      .min(3, { message: "Reason must be at least 3 characters" })
      .max(200, { message: "Reason must be less than 200 characters" }),
    leaveType: z.nativeEnum(LeaveType),
    startDate: z.string().min(1, { message: "Start date is required" }),
    endDate: z.string().nullable(),
  })
  .refine(
    (data) => {
      // If it's a full day leave, end date is required
      if (data.leaveType === LeaveType.FULLDAY && !data.endDate) {
        return false;
      }
      // For half day leave, if startDate is set, automatically set endDate to the same value
      if (
        data.leaveType === LeaveType.HALFDAY &&
        data.startDate &&
        !data.endDate
      ) {
        data.endDate = data.startDate;
      }
      return true;
    },
    {
      message: "End date is required for full day leave",
      path: ["endDate"],
    }
  );

// Type for leave request form
export type LeaveRequestFormData = z.infer<typeof leaveRequestSchema>;

// Default values for leave request form
export const defaultLeaveRequestValues: LeaveRequestFormData = {
  reason: "",
  leaveType: LeaveType.FULLDAY,
  startDate: "",
  endDate: null,
};

// Default export to satisfy Expo Router
export default {
  LeaveType,
  LeaveStatus,
  leaveRequestSchema,
  defaultLeaveRequestValues,
};
