import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView,
  FlatList,
} from "react-native";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useForm, useField<PERSON><PERSON>y, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import {
  Form,
  FormInput,
  FormDatePicker,
  FormSubmitButton,
  FormImagePicker,
} from "@/components/forms";
import {
  allowancesSchema,
  AllowancesFormData,
  defaultAllowancesValues,
  defaultAllowanceEntry,
} from "@/schemas/allowances";
import { FilterButton } from "./leave";

const statusFilterOptions = [
  { id: "allowance", label: "Allowance" },
  { id: "expense", label: "Expense" },
  { id: "travel", label: "Travel" },
  { id: "site", label: "Site" },
];

export default function ClaimsScreen() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeStatusFilter, setActiveStatusFilter] = useState("allowance");

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<AllowancesFormData>({
    defaultValues: defaultAllowancesValues,
    resolver: zodResolver(allowancesSchema),
    mode: "onChange",
  });

  // Use fieldArray to handle multiple allowance entries
  const { fields, append, remove } = useFieldArray({
    control,
    name: "allowances",
  });

  // Handle form submission
  const onSubmit = (data: AllowancesFormData) => {
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      Alert.alert("Success", "Allowances submitted successfully!", [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ]);
    }, 1500);
  };

  // Add a new allowance entry
  const handleAddMore = () => {
    append(defaultAllowanceEntry);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      <View style={styles.filterContainer}>
        {/* Status Filters */}
        <Text style={styles.filterLabel}>Claim Type:</Text>
        <FlatList
          data={statusFilterOptions}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterList}
          renderItem={({ item }) => (
            <FilterButton
              option={item}
              isActive={activeStatusFilter === item.id}
              onPress={setActiveStatusFilter}
            />
          )}
        />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        {fields.map((field, index) => (
          <View key={field.id} style={styles.allowanceContainer}>
            {index > 0 && (
              <>
                <View style={styles.separator} />
                <View style={styles.allowanceHeader}>
                  <Text style={styles.allowanceTitle}>
                    Allowance {index + 1}
                  </Text>
                  <TouchableOpacity
                    onPress={() => remove(index)}
                    style={styles.removeButton}
                  >
                    <Ionicons
                      name="close-circle"
                      size={24}
                      color={Colors.error || "red"}
                    />
                  </TouchableOpacity>
                </View>
              </>
            )}

            {/* Date Input */}
            <FormDatePicker
              name={`allowances.${index}.date`}
              control={control}
              label="Date"
              placeholder="dd-mm-yy"
            />

            {/* Day Input */}
            <FormInput
              name={`allowances.${index}.day`}
              control={control}
              label="Day"
              placeholder="Enter"
            />

            {/* Job No Input */}
            <FormInput
              name={`allowances.${index}.jobNo`}
              control={control}
              label="Job No"
              placeholder="Enter job no"
            />

            {/* Reason Input */}
            <FormInput
              name={`allowances.${index}.reason`}
              control={control}
              label="Reason"
              placeholder="Type something..."
              multiline
              numberOfLines={3}
            />

            {/* Working Hours Input */}
            <FormInput
              name={`allowances.${index}.workingHours`}
              control={control}
              label="Working Hours"
              placeholder="Enter Hours"
              keyboardType="numeric"
            />

            {/* Amount Input */}
            <FormInput
              name={`allowances.${index}.amount`}
              control={control}
              label="Amount (RM)"
              placeholder="Enter Amount"
              keyboardType="numeric"
            />

            {/* Total Input */}
            <FormInput
              name={`allowances.${index}.total`}
              control={control}
              label="Total"
              placeholder="Enter amount"
              keyboardType="numeric"
            />

            {/* Upload Invoice */}
            <FormImagePicker
              name={`allowances.${index}.invoice`}
              control={control}
              label="Upload Invoice"
            />
          </View>
        ))}

        {/* Add More Button */}
        <TouchableOpacity
          style={styles.addMoreButton}
          onPress={handleAddMore}
          activeOpacity={0.7}
        >
          <Ionicons name="add-circle" size={20} color={Colors.primary} />
          <Text style={styles.addMoreText}>Add More</Text>
        </TouchableOpacity>

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Submit"
          onSubmit={handleSubmit(onSubmit)}
          isSubmitting={isSubmitting}
          isValid={isValid}
          style={styles.submitButton}
        />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 120, // Increased bottom padding
    paddingTop: 20,
  },

  allowanceContainer: {
    marginBottom: 20,
    paddingVertical: 16,
  },
  allowanceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  allowanceTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
  },
  removeButton: {
    padding: 4,
  },
  addMoreButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    marginBottom: 20,
  },
  addMoreText: {
    marginLeft: 8,
    fontSize: 16,
    color: Colors.primary,
    fontWeight: "500",
  },
  submitButton: {
    marginTop: 20,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.border,
    marginVertical: 16,
  },
  filterContainer: {
    paddingVertical: 8,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.textSecondary,
    marginLeft: 16,
    marginTop: 8,
    marginBottom: 4,
  },
  filterList: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
});
