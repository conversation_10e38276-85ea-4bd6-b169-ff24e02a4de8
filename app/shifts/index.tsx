import { StatusBar } from "@/components/ui/StatusBar";
import { Colors } from "@/constants/Colors";
import { useGetShiftsByUserQuery } from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";
import { Ionicons } from "@expo/vector-icons";
import { Link, useRouter } from "expo-router";
import React, { useEffect, useMemo, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { Calendar, DateData } from "react-native-calendars";
import { ShiftAgendaItem, shiftAgendaItemMapper } from "../../types";
import LoadingScreen from "@/components/LoadingView";
import { endOfDay, format, startOfDay } from "date-fns";
import { isValidOrCurrentDate } from "@/lib/utils";

export default function ShiftsScreen() {
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState(new Date().toDateString());

  const markedDates = useMemo(() => {
    return {
      [selectedDate]: {
        selected: true,
        disableTouchEvent: true,
        selectedDotColor: "orange",
      },
    };
  }, [selectedDate]);

  const { session: user } = useSession();

  // TODO: add support for date
  const { data: selectedDayShifts, isLoading: loading } =
    useGetShiftsByUserQuery(
      {
        shiftsInput: {
          userId: user!.userId,
          startDateTime: startOfDay(selectedDate),
          endDateTime: endOfDay(selectedDate),
        },
      },
      { initialData: { getUserShifts: [] } }
    );

  const renderShiftItem = ({ item }: { item: ShiftAgendaItem }) => {
    return (
      <Link href={`/shifts/${item.shiftId}`} style={styles.shiftItem}>
        <View style={styles.shiftDetails}>
          <Text style={styles.assignedGuards}>Assigned :</Text>
          <Text style={styles.shiftName}>{item.name}</Text>
          <Text style={styles.shiftLocation}>{item.location}</Text>
        </View>
        <View>
          <Text style={styles.shiftTime}>
            {item.startTime} - {item.endTime}
          </Text>
        </View>
        <Ionicons
          name="chevron-forward"
          size={20}
          color={Colors.textSecondary}
        />
      </Link>
    );
  };

  const handleBackPress = () => {
    router.back();
  };

  if (loading) return <LoadingScreen />;
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <Ionicons name="chevron-back" size={28} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Shift History</Text>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : (
        <View style={styles.contentContainer}>
          {/* Calendar */}
          <Calendar
            onDayPress={(day: DateData) => {
              setSelectedDate(day.dateString);
            }}
            markedDates={markedDates}
            enableSwipeMonths={true}
            theme={{
              calendarBackground: Colors.white,
              textSectionTitleColor: Colors.textSecondary,
              selectedDayBackgroundColor: Colors.primary,
              selectedDayTextColor: Colors.white,
              todayTextColor: Colors.primary,
              dayTextColor: Colors.text,
              textDisabledColor: Colors.textLight,
              dotColor: Colors.primary,
              selectedDotColor: Colors.white,
              arrowColor: Colors.primary,
              monthTextColor: Colors.text,
              indicatorColor: Colors.primary,
              textDayFontWeight: "400",
              textMonthFontWeight: "bold",
              textDayHeaderFontWeight: "500",
            }}
          />

          {/* Shifts for selected day */}
          <View style={styles.shiftsContainer}>
            <Text style={styles.sectionTitle}>
              Shifts for{" "}
              <Text style={styles.selectedDate}>
                {format(isValidOrCurrentDate(selectedDate), "dd MMM yyyy")}
              </Text>
            </Text>

            {selectedDayShifts!.getUserShifts!.length > 0 ? (
              <FlatList
                data={selectedDayShifts!.getUserShifts.map(
                  shiftAgendaItemMapper
                )}
                renderItem={renderShiftItem}
                keyExtractor={(item) => item.shiftId}
                contentContainerStyle={styles.shiftsList}
              />
            ) : (
              <View style={styles.emptyShifts}>
                <Text style={styles.emptyShiftsText}>
                  No shifts scheduled for this day
                </Text>
              </View>
            )}
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.white,
    flex: 1,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255,255,255,0.2)",
    alignItems: "center",
    justifyContent: "center",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  contentContainer: {
    flex: 1,
  },
  shiftsContainer: {
    flex: 1,
    padding: 16,
    backgroundColor: Colors.background,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 16,
  },
  shiftsList: {
    paddingBottom: 20,
  },
  shiftItem: {
    backgroundColor: Colors.white,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    flexDirection: "column",
    alignItems: "center",
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
    elevation: 2,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  shiftTime: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: "500",
  },
  selectedDate: {
    fontSize: 18,
    fontWeight: "800",
    color: Colors.primary,
    marginBottom: 16,
  },

  shiftDetails: {
    flex: 1,
  },
  shiftName: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    marginBottom: 4,
  },
  assignedGuards: {
    fontSize: 16,
    fontWeight: "800",
    color: Colors.text,
    marginBottom: 4,
  },
  shiftLocation: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  emptyShifts: {
    height: 100,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    borderStyle: "dashed",
    marginTop: 8,
  },
  emptyShiftsText: {
    color: Colors.textSecondary,
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 16,
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: "500",
  },
});
