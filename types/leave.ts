/**
 * Leave type enum
 */
export enum LeaveType {
  FULLDAY = 'fullday',
  HALFDAY = 'halfday',
}

/**
 * Leave status enum
 */
export enum LeaveStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

/**
 * Leave interface
 */
export interface Leave {
  id: string;
  user?: string;
  reason: string;
  leaveType: LeaveType;
  startDateTime: Date;
  endDateTime: Date;
  leaveStatus?: LeaveStatus;
  rejectedReason?: string;
  approvedBy?: string;
  approvedOn?: Date;
  rejectedBy?: string;
  rejectedOn?: Date;
  requestedOn?: Date;
}
