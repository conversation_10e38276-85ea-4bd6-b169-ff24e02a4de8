import { GetShiftsByUserQuery } from "@/generated/graphql";
import { format } from "date-fns";

export interface Location {
  _id: string;
  name: string;
  address: string;
}

export interface User {
  _id: string;
  name: string;
  email: string;
}

export interface Shift {
  _id: string;
  location: Location | string;
  startDateTime: Date | string;
  endDateTime: Date | string;
  overTime?: Date | string;
  users: (User | string)[];
  isRecurring?: boolean;
  recurringId?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// For the agenda calendar
export interface ShiftAgendaItem {
  name: string;
  day: string;
  location: string;
  startTime: string;
  endTime: string;
  shiftId: string;
}

export const shiftAgendaItemMapper = (
  shift: GetShiftsByUserQuery["shiftsByUser"][number]
): ShiftAgendaItem => {
  return {
    day: format(shift.startDateTime, "dd EEE MM"),
    name: shift.users?.map((user) => user.fullname).join(",") || "",
    startTime: format(shift.startDateTime, "dd EEE hh:mm a"),
    endTime: format(shift.endDateTime, "dd EEE hh:mm a"),
    location: shift.location?.name || "",
    shiftId: shift.id,
  };
};
